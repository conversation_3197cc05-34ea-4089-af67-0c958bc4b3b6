<template>
  <div class="device-tabs">
    <el-tabs v-model="activeTab" @tab-click="tabClick">
      <el-tab-pane :name="t('matrix.deviceList.title')" :lazy="true">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><List /></el-icon>
            <span>{{ t("matrix.deviceList.title") }}</span>
          </span>
        </template>
        <div v-show="activeTab == t('matrix.deviceList.title')">
          <!-- <DeviceList></DeviceList> -->
          <Suspense>
            <!-- 异步组件 -->
            <template #default>
              <component :is="comps.DeviceList" />
            </template>

            <!-- 加载中的占位内容 -->
            <template #fallback>
              <SkeletonLoading style="width: 100%; height: 100%" />
            </template>
          </Suspense>
        </div>
      </el-tab-pane>
      <el-tab-pane :name="t('matrix.downList.title')" :lazy="true">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Document /></el-icon>
            <span>{{ t("matrix.downList.title") }}</span>
          </span>
        </template>
        <div v-show="activeTab == t('matrix.downList.title')" :lazy="true">
          <!-- <DownList></DownList> -->

          <Suspense>
            <!-- 异步组件 -->
            <template #default>
              <component :is="comps.DownList" />
            </template>

            <!-- 加载中的占位内容 -->
            <template #fallback>
              <SkeletonLoading style="width: 100%; height: 100%" />
            </template>
          </Suspense>
        </div>
      </el-tab-pane>
      <el-tab-pane :name="t('matrix.paramList.title')">
        <template #label>
          <span class="custom-tabs-label">
            <el-icon><Collection /></el-icon>
            <span>{{ t("matrix.paramList.title") }}</span>
          </span>
        </template>
        <div v-show="activeTab == t('matrix.paramList.title')">
          <!-- <ParamList></ParamList> -->
          <Suspense>
            <!-- 异步组件 -->
            <template #default>
              <component :is="comps.ParamList" />
            </template>

            <!-- 加载中的占位内容 -->
            <template #fallback>
              <SkeletonLoading style="width: 100%; height: 100%" />
            </template>
          </Suspense>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { TabsPaneContext } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

import SkeletonLoading from "@/components/SkeletonLoading.vue";

const comps = {
  DeviceList: defineAsyncComponent(() => import("../views/DeviceList.vue")),
  DownList: defineAsyncComponent(() => import("../views/DownList.vue")),
  ParamList: defineAsyncComponent(() => import("../views/ParamList.vue"))
};
// import DeviceList from "../views/DeviceList.vue";
// import DownList from "../views/DownList.vue";
// import ParamList from "../views/ParamList.vue";
import { Collection, Document, List } from "@element-plus/icons-vue";

const activeTab = computed(() => t("matrix.deviceList.title"));
const tabClick = (tabItem: TabsPaneContext) => {
  const fullPath = tabItem.props.name as string;
  console.log(fullPath);
};
</script>

<style scoped lang="scss">
.device-tabs {
  width: 100%;
  height: 100%;
  padding-bottom: 3px;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs) {
    height: 100%;
    .el-tab-pane {
      height: 100%;
    }
  }
}
.device-tabs > .el-tabs__content {
  padding: 32px;
  font-size: 32px;
  font-weight: 600;
  color: #6b778c;
}
.not-img {
  margin-right: 50px;
}
.device-tabs .custom-tabs-label .el-icon {
  vertical-align: middle;
}
.device-tabs .custom-tabs-label span {
  margin-left: 4px;
  vertical-align: middle;
}
.device-item-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: auto;
  height: 100%;
  color: var(--el-text-color-secondary);
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
}
.loading-placeholder {
  height: 100vh;
  padding: 20px;
  text-align: center;
}
.spinner {
  width: 24px;
  height: 24px;
  margin: 0 auto;
  border: 3px solid #dddddd;
  border-top-color: #646cff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
