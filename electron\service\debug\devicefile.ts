import { logger } from "ee-core/log";
import { IECReq } from "../../interface/debug/request";
import ClientDeviceGlobal from "../../data/debug/globalDeviceData";
import {
  FileDirRequestData,
  FileReadError,
  FileReadRequestData,
  FileReadTransferData,
  FileReadTransferStatus,
  FileWriteError,
  FileWriteInfo,
  FileWriteRequestData,
  FileWriteStatus,
  UpadRpcFileItem,
} from "iec-upadrpc/dist/src/data";
import { IEC_EVENT, IECNotify } from "../../data/debug/notify";
import { getMainWindow } from "ee-core/electron";
import { sendMessageToUIByNotify } from "../../utils/iecUiUtils";
import { getUUID } from "../../utils/common";
import { Column } from "../../interface/debug/exportTypes";
import { ExcelExporter } from "../../utils/excelUtils";
import { SingleGlobalDeviceInfo } from "../../data/debug/singleGlobalDeviceInfo";
import { t } from "../../data/i18n/i18n";

/**
 * 装置文件服务
 * <AUTHOR>
 * @class
 */
class DeviceFileService {
  private excelExporter: ExcelExporter;

  constructor() {
    this.excelExporter = new ExcelExporter();
  }
  /**
   * 获取装置文件
   * @param req
   * @returns
   */
  async getDeviceFile(req: IECReq<any>): Promise<any[]> {
    logger.info(
      "[DeviceFileService] getDeviceFile input:",
      JSON.stringify(req)
    );
    try {
      logger.debug("[DeviceFileService] getDeviceFile - 获取设备信息");
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = await device?.deviceClient;
      const { filePath } = req.data;
      const requestData: FileDirRequestData = {
        pathName: filePath,
      };

      client?.selectActiveSG
      const response = await client?.readFileDir(requestData);
      if (response?.isSuccess()) {
        // logger.info("response.data.fileEntry:", response.data.fileEntry);
        response.data.fileEntry.sort((a, b) => {
          var index1 =
            a.fileName.indexOf("_") > 0
              ? a.fileName.substring(0, a.fileName.indexOf("_"))
              : 0;
          var index2 =
            b.fileName.indexOf("_") > 0
              ? b.fileName.substring(0, b.fileName.indexOf("_"))
              : 0;
          return Number(index2) - Number(index1);
        });
        // logger.info(
        //   "[DeviceFileService] getDeviceFile return:",
        //   response.data.fileEntry
        // );
        return response.data.fileEntry.filter(
          (item) => !item.fileName.endsWith("/")
        );
        // return response.data.fileEntry;
      }
      throw new Error(String(response?.msg));
    } catch (error) {
      logger.error("[DeviceFileService] getDeviceFile error:", error);
      throw error;
    }
  }

  /**
   * 上传装置文件
   * @param req
   * @returns
   */
  async uploadDeviceFile(req: IECReq<any>): Promise<boolean> {
    logger.info("[DeviceFileService] uploadDeviceFile input:", req);
    try {
      logger.debug("[DeviceFileService] uploadDeviceFile - 开始上传");
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;

      const { fileItems, savePath, cb, taskid } = req.data;

      // 支持外部传入taskid
      const abortController = new AbortController();
      const uuid = taskid || getUUID();
      device?.abortControllerMap.set(uuid, abortController);
      device?.abortTaskMap.set(uuid, fileItems);
      const param: FileReadRequestData = {
        fileItems: fileItems,
        savePath: savePath,
        cb: (result: FileReadTransferData) => {
          let msg = this.getUploadErrMsg(result, device);
          const data = {
            fileItem: result.fileItem,
            errorMsg: msg,
            status: result.status,
            progress: result.progress,
            taskid: uuid,
          };
          const notify: IECNotify = {
            type: "fileUpload",
            data: data,
          };
          sendMessageToUIByNotify(
            IEC_EVENT.FILEUPLOAD_NOTIFY,
            notify,
            getMainWindow()
          );
          // 若外部有cb则调用
          if (typeof cb === "function") {
            cb({ ...result, errorMsg: msg, taskid: uuid });
          }
        },
      };
      const result = await client?.readFile(param, abortController);
      device?.abortControllerMap.delete(uuid);
      device?.abortTaskMap.delete(uuid);
      if (result?.isSuccess()) {
        logger.info(
          "[DeviceFileService] uploadDeviceFile " +
            t("deviceFile.uploadSuccess")
        );
        return true;
      } else {
        let msg = this.getUploadErrMsg(result?.data, device);
        throw new Error(msg);
      }
    } catch (error) {
      logger.error("[DeviceFileService] uploadDeviceFile failed", { error });
      throw error;
    }
  }

  /**
   * 取消上传装置文件
   * @param req
   * @returns
   */
  async cancelUploadDeviceFIle(req: IECReq<any>): Promise<any[]> {
    logger.info("[DeviceFileService] cancelUploadDeviceFIle input:", req);
    try {
      logger.debug("[DeviceFileService] cancelUploadDeviceFIle - 开始取消");
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const { taskids } = req.data;
      const items: UpadRpcFileItem[] = [];
      taskids.forEach((taskid) => {
        const abortController = device?.abortControllerMap.get(taskid);
        if (abortController != undefined) {
          abortController.abort();
          device?.abortControllerMap.delete(taskid);
          const taskItems = device?.abortTaskMap.get(taskid) || [];
          items.push(...taskItems);
          device?.abortTaskMap.delete(taskid);
        }
      });
      logger.info(items);
      logger.info(
        "[DeviceFileService] cancelUploadDeviceFIle " +
          t("deviceFile.cancelSuccess")
      );
      return items;
    } catch (error) {
      logger.error("[DeviceFileService] cancelUploadDeviceFIle failed", {
        error,
      });
      throw error;
    }
  }

  /**
   * 下载装置文件
   * @param req
   * @returns
   */
  async downloadDeviceFile(req: IECReq<any>): Promise<any> {
    logger.info("[DeviceFileService] downloadDeviceFile input:", req);
    try {
      logger.debug("[DeviceFileService] downloadDeviceFile - 开始下载");
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;
      const { fileItems, remoteParentPath } = req.data;
      // 缓存i起来
      const abortController = new AbortController();
      const uuid = getUUID();
      device?.abortControllerMap.set(uuid, abortController);
      device?.abortTaskMap.set(uuid, fileItems);
      const param: FileWriteRequestData = {
        fileItems: fileItems,
        remoteParentPath: remoteParentPath,
        verifyType: "None",
        cb: (result: FileWriteInfo) => {
          let msg = this.getDownloadErrMsg(result, device);
          const data = {
            fileItem: result.currentfileItem,
            errorMsg: msg,
            status: result.status,
            progress: result.progress,
            taskid: uuid,
          };
          const notify: IECNotify = {
            type: "fileDownload",
            data: data,
          };
          sendMessageToUIByNotify(
            IEC_EVENT.FILEDOWNLOAD_NOTIFY,
            notify,
            getMainWindow()
          );
        },
      };
      const result = await client?.writeFile(param, abortController);
      console.log(result);
      device?.abortControllerMap.delete(uuid);
      device?.abortTaskMap.delete(uuid);
      if (result?.status == FileWriteStatus.ALL_FILE_FINISH) {
        logger.info(
          "[DeviceFileService] downloadDeviceFile " +
            t("deviceFile.downloadSuccess")
        );
        return true;
      } else {
        let msg = this.getDownloadErrMsg(result, device);
        throw new Error(msg);
      }
    } catch (error) {
      logger.error("[DeviceFileService] downloadDeviceFile failed", { error });
      throw error;
    }
  }
  private getUploadErrMsg(
    result: FileReadTransferData | undefined,
    device: SingleGlobalDeviceInfo | undefined
  ) {
    let msg = "";
    if (result?.status == FileReadTransferStatus.ERROR) {
      if (result.errCode === FileReadError.SERVICE_ERROR) {
        const errdata = result?.err;
        logger.info(result);
        if (Number.isInteger(errdata)) {
          msg = device?.getServiceErrMsgByCode(String(errdata)) || "";
        }
      } else {
        const errdata = result?.err;
        if (errdata instanceof Error) {
          msg = errdata.message;
        }
      }
    }
    return msg;
  }
  private getDownloadErrMsg(
    result: FileWriteInfo | undefined,
    device: SingleGlobalDeviceInfo | undefined
  ) {
    let msg = "";
    if (result?.status == FileWriteStatus.ERROR) {
      if (result.errorCode === FileWriteError.SERVICE_ERROR) {
        const errdata = result?.errorData;
        logger.info(result);
        if (Number.isInteger(errdata)) {
          msg = device?.getServiceErrMsgByCode(String(errdata)) || "";
        }
      } else {
        const errdata = result?.errorData;
        if (errdata instanceof Error) {
          msg = errdata.message;
        }
      }
    }
    return msg;
  }

  /**
   * 取消下载装置文件
   * @param req
   * @returns
   */
  async cancelDownloadDeviceFile(req: IECReq<any>): Promise<any[]> {
    try {
      logger.info("[DeviceFileService] cancelDownloadDeviceFile", req);
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const { taskids } = req.data;
      const items: UpadRpcFileItem[] = [];
      taskids.forEach((taskid) => {
        const abortController = device?.abortControllerMap.get(taskid);
        if (abortController != undefined) {
          abortController.abort();
          device?.abortControllerMap.delete(taskid);
          const taskItems = device?.abortTaskMap.get(taskid) || [];
          items.push(...taskItems);
          device?.abortTaskMap.delete(taskid);
        }
      });
      logger.info(items);
      return items;
    } catch (error) {
      logger.error("[DeviceFileService] cancelDownloadDeviceFile failed", {
        error,
      });
      throw error;
    }
  }

  async importDownloadDeviceFile(req: IECReq<any>): Promise<any[]> {
    try {
      logger.info("[DeviceFileService] importDownloadDeviceFile", req);
      const { path } = req.data;

      logger.info("path:", path);

      // 定义 keyMapping
      const keyMapping = {
        [t("deviceFile.headers.fileName")]: "fileName",
        [t("deviceFile.headers.fileSize")]: "fileSizeAs",
        [t("deviceFile.headers.filePath")]: "path",
        [t("deviceFile.headers.lastModified")]: "lastModified",
      };

      const parsedData = await this.excelExporter.parseExcel(path, keyMapping);
      return parsedData;
    } catch (error) {
      logger.error("[DeviceFileService] importDownloadDeviceFile error", {
        error,
      });
      throw error;
    }
  }

  async exportDownloadDeviceFile(req: IECReq<any>): Promise<boolean> {
    try {
      const { data, path } = req.data;
      logger.info("[DeviceFileService] exportDownloadDeviceFile");

      const columns: Column[] = [
        { header: t("deviceFile.headers.index"), key: "id", width: 10 },
        {
          header: t("deviceFile.headers.fileName"),
          key: "fileName",
          width: 20,
        },
        {
          header: t("deviceFile.headers.fileSize"),
          key: "fileSizeAs",
          width: 30,
        },
        { header: t("deviceFile.headers.filePath"), key: "path", width: 50 },
        {
          header: t("deviceFile.headers.lastModified"),
          key: "lastModified",
          width: 50,
        },
      ];

      if (String(path).endsWith("xlsx")) {
        await this.excelExporter.exportToExcel(
          data,
          columns,
          path,
          t("deviceFile.downloadFileList")
        );
      }
      return true;
    } catch (error) {
      logger.error("[DeviceFileService] exportDownloadDeviceFile error", error);
      return false;
    }
  }
}

DeviceFileService.toString = () => "[class DeviceFileService]";
const deviceFileService = new DeviceFileService();

export { DeviceFileService, deviceFileService };
