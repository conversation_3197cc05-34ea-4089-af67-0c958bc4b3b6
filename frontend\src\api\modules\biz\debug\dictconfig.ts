import { 
  ProjectDictRequestData, 
  ProjectDictRequestRes, 
  SetProjectDictRequestData, 
  SetProjectDictRes 
} from "@/api/interface/biz/debug/dictinfo";
import { useDebugStore } from "@/stores/modules/debug";
import { IECResult } from "iec-common";

/**
 * 词条配置API模块
 */
const dictConfigApi = {
  /**
   * 获取工程词条
   * @param requestData 请求参数
   * @returns 词条数据
   */
  async getProjectDict(requestData: ProjectDictRequestData): Promise<IECResult<ProjectDictRequestRes>> {
    const debugStore = useDebugStore();
    const client = debugStore.getCurrentClient();
    
    if (!client) {
      return {
        isSuccess: () => false,
        msg: "设备未连接",
        error: "Device not connected",
        data: null as any
      };
    }

    try {
      const result = await client.getProjectDict(requestData);
      return result;
    } catch (error) {
      console.error("获取词条失败:", error);
      return {
        isSuccess: () => false,
        msg: "获取词条失败",
        error: error instanceof Error ? error.message : "Unknown error",
        data: null as any
      };
    }
  },

  /**
   * 设置工程词条
   * @param requestData 请求参数
   * @returns 设置结果
   */
  async setProjectDict(requestData: SetProjectDictRequestData): Promise<IECResult<SetProjectDictRes>> {
    const debugStore = useDebugStore();
    const client = debugStore.getCurrentClient();
    
    if (!client) {
      return {
        isSuccess: () => false,
        msg: "设备未连接",
        error: "Device not connected",
        data: null as any
      };
    }

    try {
      const result = await client.setProjectDict(requestData);
      return result;
    } catch (error) {
      console.error("设置词条失败:", error);
      return {
        isSuccess: () => false,
        msg: "设置词条失败",
        error: error instanceof Error ? error.message : "Unknown error",
        data: null as any
      };
    }
  }
};

export { dictConfigApi };
