<script setup lang="ts">
import _ from "lodash";
import {
  convertCelsiusToKelvin,
  convertDelisleToKelvin,
  convertFahrenheitToKelvin,
  convertKelvinToCelsius,
  convertKelvinToDelisle,
  convertKelvinToFahrenheit,
  convertKelvinToNewton,
  convertKelvinToRankine,
  convertKelvinToReaumur,
  convertKelvinToRomer,
  convertNewtonToKelvin,
  convertRankineToKelvin,
  convertReaumurToKelvin,
  convertRomerToKelvin
} from "./temperature-converter.models";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

type TemperatureScale = "kelvin" | "celsius" | "fahrenheit" | "rankine" | "delisle" | "newton" | "reaumur" | "romer";

// 分离数据和翻译，数据部分使用 reactive
const unitsData = reactive<
  Record<
    string | TemperatureScale,
    {
      ref: number;
      toKelvin: (v: number) => number;
      fromKelvin: (v: number) => number;
    }
  >
>({
  kelvin: {
    ref: 0,
    toKelvin: _.identity,
    fromKelvin: _.identity
  },
  celsius: {
    ref: 0,
    toKelvin: convertCelsiusToKelvin,
    fromKelvin: convertKelvinToCelsius
  },
  fahrenheit: {
    ref: 0,
    toKelvin: convertFahrenheitToKelvin,
    fromKelvin: convertKelvinToFahrenheit
  },
  rankine: {
    ref: 0,
    toKelvin: convertRankineToKelvin,
    fromKelvin: convertKelvinToRankine
  },
  delisle: {
    ref: 0,
    toKelvin: convertDelisleToKelvin,
    fromKelvin: convertKelvinToDelisle
  },
  newton: {
    ref: 0,
    toKelvin: convertNewtonToKelvin,
    fromKelvin: convertKelvinToNewton
  },
  reaumur: {
    ref: 0,
    toKelvin: convertReaumurToKelvin,
    fromKelvin: convertKelvinToReaumur
  },
  romer: {
    ref: 0,
    toKelvin: convertRomerToKelvin,
    fromKelvin: convertKelvinToRomer
  }
});

// 翻译部分使用 computed，确保语言切换时能响应式更新
const units = computed(() => ({
  kelvin: {
    title: t("tools.temperature.kelvin"),
    unit: t("tools.temperature.kelvinUnit"),
    ...unitsData.kelvin
  },
  celsius: {
    title: t("tools.temperature.celsius"),
    unit: t("tools.temperature.celsiusUnit"),
    ...unitsData.celsius
  },
  fahrenheit: {
    title: t("tools.temperature.fahrenheit"),
    unit: t("tools.temperature.fahrenheitUnit"),
    ...unitsData.fahrenheit
  },
  rankine: {
    title: t("tools.temperature.rankine"),
    unit: t("tools.temperature.rankineUnit"),
    ...unitsData.rankine
  },
  delisle: {
    title: t("tools.temperature.delisle"),
    unit: t("tools.temperature.delisleUnit"),
    ...unitsData.delisle
  },
  newton: {
    title: t("tools.temperature.newton"),
    unit: t("tools.temperature.newtonUnit"),
    ...unitsData.newton
  },
  reaumur: {
    title: t("tools.temperature.reaumur"),
    unit: t("tools.temperature.reaumurUnit"),
    ...unitsData.reaumur
  },
  romer: {
    title: t("tools.temperature.romer"),
    unit: t("tools.temperature.romerUnit"),
    ...unitsData.romer
  }
}));

function update(key: TemperatureScale): void {
  const { ref: value, toKelvin } = units.value[key];

  const kelvins = toKelvin(value) ?? 0;

  _.chain(units.value)
    .omit(key)
    .forEach(({ fromKelvin }, index) => {
      unitsData[index].ref = Math.floor((fromKelvin(kelvins) ?? 0) * 100) / 100;
    })
    .value();
}

update("kelvin");
</script>

<template>
  <div class="head">
    {{ t("tools.temperature.title") }}
    <div class="sub-head">{{ t("tools.temperature.description") }}</div>
  </div>
  <div class="base-converter">
    <el-input v-for="[key, { title, unit }] in Object.entries(units)" :key="key">
      <template #prepend>
        <div style="width: 70px; color: var(--el-menu-text-color)">{{ title }}</div>
      </template>

      <template #append>
        <el-input-number v-model="unitsData[key].ref" controls-position="right" style="width: 100%" @change="() => update(key as TemperatureScale)" />
        <div style="width: 20px; color: var(--el-menu-text-color)">{{ unit }}</div>
      </template>
    </el-input>
  </div>
</template>

<style lang="scss" scoped>
.head {
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  .sub-head {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: var(--el-text-color-regular);
  }
}
.base-converter {
  @include flex(column, flex-start, stretch);

  gap: 12px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 4%);
  :deep(.el-input) {
    height: 44px;
    font-size: 14px;
    border-radius: 6px;
    transition: all 0.2s ease;
    &:hover {
      .el-input__wrapper {
        box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
      }
    }
    &:focus-within {
      .el-input__wrapper {
        box-shadow: 0 0 0 1px var(--el-color-primary) inset;
      }
    }
    .el-input__wrapper {
      border-radius: 6px;
      box-shadow: 0 0 0 1px var(--el-border-color) inset;
    }
    .el-input-group__prepend {
      background: var(--el-fill-color-light);
      border-right: 1px solid var(--el-border-color);
      border-radius: 6px 0 0 6px;
      div {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        font-weight: 500;
        color: var(--el-text-color-regular);
      }
    }
    .el-input-group__append {
      background: var(--el-fill-color-light);
      border-left: 1px solid var(--el-border-color);
      border-radius: 0 6px 6px 0;
      .el-input-number {
        border: none;
        .el-input__wrapper {
          background: transparent;
          box-shadow: none;
        }
        .el-input__inner {
          font-weight: 500;
          text-align: center;
        }
      }
      div:last-child {
        margin-left: 8px;
        font-size: 12px;
        font-weight: 500;
        color: var(--el-text-color-regular);
      }
    }
  }
}
</style>
