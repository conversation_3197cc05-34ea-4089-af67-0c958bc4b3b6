<template>
  <v-contextmenu ref="contextmenu">
    <v-contextmenu-item @click="sameSearch">
      <svg-icon icon="ant-design:search-outlined" class="menu-svg" />
      <span>{{ t("report.sameSearch") }}</span>
    </v-contextmenu-item>
    <v-contextmenu-item @click="sameFilter">
      <svg-icon icon="ant-design:filter-outlined" class="menu-svg" />
      <span>{{ t("report.sameFilter") }}</span>
    </v-contextmenu-item>
    <v-contextmenu-divider></v-contextmenu-divider>
    <v-contextmenu-item @click="dealTimeCol">
      <svg-icon icon="ant-design:field-time-outlined" class="menu-svg" />
      <span>{{ t("report.showHideTime") }}</span>
    </v-contextmenu-item>
  </v-contextmenu>
  <div class="table-main report-page">
    <div class="flex flex-wrap header card button-group">
      <el-form :inline="true" class="report-query-form" style="width: 100%">
        <el-form-item>
          <el-checkbox v-model="isDateCheck">{{ t("report.date") }}：</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="dateRange" type="datetimerange"></el-date-picker>
        </el-form-item>
        <el-form-item>
          {{ t("report.searchType") }}：
          <el-select v-model="searchType" style="width: 80px">
            <el-option v-for="item in searchTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input v-model="searchInfo" clearable style="width: 150px"></el-input>
        </el-form-item>
        <el-form-item>
          <el-text type="primary">{{ t("report.total", { num: totalNum }) }}</el-text>
        </el-form-item>
      </el-form>
      <div class="header-button-ri" style="width: 100%; margin-top: 4px; text-align: right">
        <el-button type="primary" :icon="Search" :disabled="isButtonClick" @click="searchReport">{{ t("report.search") }}</el-button>
        <el-button type="success" :icon="Folder" :disabled="isButtonClick" @click="exportReport">{{ t("report.save") }}</el-button>
        <el-button type="primary" plain :icon="Refresh" @click="refreshReport" :loading="refreshLoading">{{ refreshName }}</el-button>
        <el-button type="danger" plain :icon="Delete" @click="clearList">{{ t("report.clearList") }}</el-button>
      </div>
    </div>
    <div class="card table-box report-table">
      <el-table
        v-loading="globalReport?.isReportLoading || false"
        v-contextmenu:contextmenu="contextmenu"
        :data="filterTableData"
        border
        :max-height="getTableMaxHeight(260)"
        :stripe="true"
        :element-loading-text="t('report.loading')"
        @cell-contextmenu="cellContextmenu"
      >
        <el-table-column :label="t('report.reportNumber')" width="100" prop="entryID" align="center"></el-table-column>
        <el-table-column v-if="showTime" :label="t('report.time')" width="300" prop="time" align="center"></el-table-column>
        <el-table-column :label="t('report.description')" prop="name" align="center">
          <template #default="scope">
            <span v-html="highLightData(scope.row.name)"></span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
  <el-dialog
    v-model="dialogShow.searchProgress"
    width="60%"
    class="hover"
    :align-center="true"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :show-close="false"
    draggable
    :title="t('report.progress')"
  >
    <div style="height: 120px; padding: 15px">
      <el-progress :percentage="dialogShow.percentage" :text-inside="false" striped striped-flow style="margin-top: 60px">
        <span>{{ dialogShow.progressText }}</span>
      </el-progress>
    </div>
  </el-dialog>
  <ProgressDialog ref="progressDialog" />
</template>
<script setup lang="ts">
import { Delete, Refresh, Search, Folder } from "@element-plus/icons-vue";
import Message from "@/scripts/message";
import { getDateZh, getTableMaxHeight } from "@/utils/index";
import { ipc } from "@/api/request/ipcRenderer";
import { genRpcTimeParam } from "@/utils/iec/iecRpcUtils";
import { IECNotify, RealEventState, ReportParam, ResultData } from "@/api";
import { filter, includes } from "lodash";
import { ContextmenuInstance } from "v-contextmenu/es/types";
import { useDebugStore } from "@/stores/modules/debug";
import { reportApi } from "@/api/modules/biz/debug/report";
import { realEventApi } from "@/api/modules/biz/debug/realevent";
import { osControlApi } from "@/api/modules/biz/os/control";
import { useConfigStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";
import { ElMessageBox } from "element-plus";
import ProgressDialog from "../dialog/ProgressDialog.vue";
const progressDialog = ref();

const { t } = useI18n();

const realEventState = ref<RealEventState>({
  subscribe: false,
  type: ""
});
const refreshLoading = ref(false);
const { paramInfo } = useConfigStore();
const { report, currDevice, addConsole } = useDebugStore();
const globalReport = report.get(currDevice.id);
const yesterday = new Date();
yesterday.setDate(yesterday.getDate() - 1);
const dateRange = ref<[Date, Date]>([yesterday, new Date()]);
const isButtonClick = ref(false);
const showTime = ref(true);
const contextmenu = ref<ContextmenuInstance>();
let currLine: any = undefined;
const cellContextmenu = (row): void => {
  currLine = row;
};
const searchInfo = ref("");
const searchType = ref(0);
const searchTypes = [
  {
    value: 0,
    label: t("report.search")
  },
  {
    value: 1,
    label: t("report.filter")
  }
];
const dialogShow = ref({
  searchProgress: false,
  percentage: 0,
  progressText: ""
});
const tableData = ref<any[]>([]);
const refreshMark = ref<boolean>(false);
const refreshName = computed(() => t("report.autoRefresh"));
const isDateCheck = ref<boolean>(false);
const totalNum = computed(() => {
  return filterTableData.value.length;
});
const filterTableData = computed(() => {
  // 如果 globalReport!.keyword不为空，按照这个关键子先过滤
  if (searchType.value == 1 && searchInfo.value) {
    return filter(tableData.value, item => includes(item.name.toLowerCase(), searchInfo.value.toLowerCase()));
  }
  return tableData.value;
});
const highLightData = (desc): void => {
  if (searchType.value == 0 && searchInfo.value) {
    const regex = new RegExp(`(${searchInfo.value})`, "gi");
    return desc.replace(regex, '<span style="color: var(--el-color-primary);font-weight: bold;">$&</span>');
  }
  return desc;
};
//  QueryHisEvtByTime: ReportCommon,
//  QueryHisFaultByTime: ReportGroup,
//  QueryOpReportByTime: ReportOperate,
//  QueryAuditLogByTime: ReportCommon
const getTableData = (): ReportParam.IECRpcCommonReportRes[] => {
  switch (globalReport!.currReportMethod) {
    case "QueryHisEvtByTime":
      return globalReport!.commonReport.get(globalReport?.newname || globalReport?.currReportType || "") || [];
    default:
      return [];
  }
};

let timerId;
const timerRefresh = (): void => {
  timerId = setTimeout(() => {
    refreshList().then(() => {
      timerRefresh();
    });
  }, paramInfo.REPORT_REFRESH_TIME);
};

const sameSearch = (): void => {
  if (!currLine) {
    Message.warning(t("report.selectRowToOperate"));
    return;
  }
  let sameDesc = currLine.name;
  if (currLine.name.includes(" ")) {
    sameDesc = currLine.name.split(" ")[0];
  }
  searchType.value = 0;
  searchInfo.value = sameDesc;
};
const sameFilter = (): void => {
  if (!currLine) {
    Message.warning(t("report.selectRowToOperate"));
    return;
  }
  let sameDesc = currLine.name;
  if (currLine.name.includes(" ")) {
    sameDesc = currLine.name.split(" ")[0];
  }
  searchType.value = 1;
  searchInfo.value = sameDesc;
};
const dealTimeCol = (): void => {
  showTime.value = !showTime.value;
};
const refreshReport = (): void => {
  refreshMark.value = !refreshMark.value;
  if (refreshMark.value == true) {
    subRealEvent();
  } else {
    unSubRealEvent();
  }
};

const clearList = (): void => {
  globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  tableData.value = [];
};

const searchReport = async (): Promise<void> => {
  if (isDateCheck.value && dateRange.value.length < 2) {
    Message.warning(t("report.selectCompleteTimeRange"));
    return;
  }
  const arg: ReportParam.IECRpcReportSearchParam = {
    type: globalReport!.currReportType,
    startTime: isDateCheck.value ? genRpcTimeParam(dateRange.value[0]) : "",
    stopTime: isDateCheck.value ? genRpcTimeParam(dateRange.value[1]) : "",
    entryAfter: "",
    orderBy: "DESC"
  };
  globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  globalReport!.isReportLoading = true;
  isButtonClick.value = true;
  dialogShow.value.percentage = 0;
  dialogShow.value.searchProgress = true; // 弹窗立即显示
  const reportDesc = globalReport!.currReportDesc;
  dialogShow.value.progressText = t("report.searchProgress", { type: reportDesc });
  startFakeProgress();
  // 保证进度条弹窗至少显示500ms
  const minShowPromise = new Promise(resolve => setTimeout(resolve, 500));
  const searchPromise = reportApi.getCommonReportList(arg);
  const [res] = await Promise.all([searchPromise, minShowPromise]);
  stopFakeProgress();
  dialogShow.value.searchProgress = false;
  if (res.code != 1) {
    Message.warning(res.msg);
    globalReport!.isReportLoading = false;
    isButtonClick.value = false;
    return;
  }
};

const refreshList = async (): Promise<void> => {
  const arg: ReportParam.IECRpcReportSearchParam = {
    type: globalReport!.currReportType,
    startTime: "",
    stopTime: "",
    orderBy: "DESC"
  };
  const res = await reportApi.refreshReport(arg);
  if (res.code != 1) {
    Message.warning(res.msg);
    return;
  }
  if (Array.isArray(res.data)) {
    globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", res.data);
    let filteredList = res.data as any;
    if (globalReport!.keyword && globalReport!.keyword.trim() !== "") {
      filteredList = res.data.filter(item => (item.name || "").toLowerCase().includes(globalReport!.keyword.toLowerCase()));
    }
    tableData.value = filteredList;
  } else {
    globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
    tableData.value = [];
  }
};

let fakeProgressTimer: any = null;
function startFakeProgress() {
  dialogShow.value.percentage = 0;
  if (fakeProgressTimer) clearInterval(fakeProgressTimer);
  fakeProgressTimer = setInterval(() => {
    if (dialogShow.value.percentage < 95) {
      dialogShow.value.percentage += 5;
    }
  }, 100);
}
function stopFakeProgress() {
  if (fakeProgressTimer) {
    clearInterval(fakeProgressTimer);
    fakeProgressTimer = null;
  }
  dialogShow.value.percentage = 100;
}
const exportReport = async (): Promise<void> => {
  if (getTableData().length == 0) {
    Message.warning(t("report.noDataToSave"));
    addConsole(t("report.exportLogFailed", { msg: t("report.noDataToSave") }));
    return;
  }
  progressDialog.value.show();
  const reportDesc: string = globalReport!.currReportDesc;
  const path = await osControlApi.openSaveFileDialogByParams({
    title: t("report.saveReport"),
    defaultPath: reportDesc + getDateZh(),
    filterList: [
      { name: "Rpt", extensions: ["rpt"] },
      { name: "Excel", extensions: ["xlsx"] }
    ]
  });
  if (!path) {
    progressDialog.value.hide();
    addConsole(t("report.exportLogFailed", { msg: t("report.saveFailed") }));
    return;
  }
  progressDialog.value.setProgress(5, t("report.exporting"), false);
  let fakeProgress = setInterval(() => {
    if (progressDialog.value.progressDialog.percentage < 95) {
      progressDialog.value.setProgress(progressDialog.value.progressDialog.percentage + 5, t("report.exporting"));
    }
  }, 100);
  const exportList: any[] = [];
  getTableData().forEach(obj => {
    exportList.push({
      entryID: obj.entryID,
      name: obj.name,
      time: obj.time
    });
  });
  const arg: ReportParam.IECRpcCommonReportExportParam = {
    type: globalReport!.currReportType,
    method: globalReport!.currReportMethod,
    path: path as unknown as string,
    items: exportList
  };
  const res: ResultData<any> = await reportApi.exportCommonReport(arg);
  clearInterval(fakeProgress);
  progressDialog.value.setProgress(100, t("report.exporting"));
  setTimeout(() => progressDialog.value.hide(), 500);
  if (res.code != 1) {
    Message.warning(res.msg);
    addConsole(t("report.exportLogFailed", { msg: res.msg }));
    return;
  }
  Message.success(t("report.saveSuccess"));
  addConsole(t("report.exportLogSuccess", { path }));
  await ElMessageBox.alert(t("report.saveSuccess"), t("report.progress"), {
    confirmButtonText: t("common.confirm") || "确定",
    type: "success"
  });
};
const notifyMethod = (_event: unknown, notify: IECNotify): void => {
  const reportData = notify.data as any;
  if (notify.type == "readCommonReport") {
    // 清空缓存
    globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
    if (reportData.code != 1) {
      globalReport!.isReportLoading = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;
      Message.success(reportData.msg);
      return;
    }
    globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
    if (Array.isArray(reportData.data)) {
      let filteredList = reportData.data;
      if (globalReport!.keyword && globalReport!.keyword.trim() !== "") {
        filteredList = reportData.data.filter(item => (item.name || "").toLowerCase().includes(globalReport!.keyword.toLowerCase()));
      }
      tableData.value = filteredList;
      globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", filteredList);
    } else {
      globalReport!.commonReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
      tableData.value = [];
    }
    globalReport!.isReportLoading = false;
    isButtonClick.value = false;
    dialogShow.value.percentage = 100;
    dialogShow.value.searchProgress = false;
  }
};

onMounted(() => {
  ipc.on("report_notify", notifyMethod);
  tableData.value = getTableData();
});

onBeforeUnmount(() => {
  if (timerId) {
    clearTimeout(timerId);
  }
  ipc.removeAllListeners("report_notify");
});

watch(
  () => refreshMark.value,
  newValue => {
    if (newValue == true) {
      refreshName.value = t("report.stopRefresh");
      isButtonClick.value = true;
      timerRefresh();
    } else {
      refreshName.value = t("report.autoRefresh");
      isButtonClick.value = false;
      if (timerId) {
        clearTimeout(timerId);
      }
    }
  }
);
watch(
  () => globalReport!.currReportDesc,
  () => {
    tableData.value = getTableData();
    isDateCheck.value = false;
    isButtonClick.value = false;
    searchInfo.value = "";
    searchType.value = 0;
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    dateRange.value[0] = yesterday;
    dateRange.value[1] = new Date();
    refreshMark.value = false;
    refreshName.value = t("report.autoRefresh");
    if (timerId) {
      clearTimeout(timerId);
    }
    unSubRealEvent();
  }
);
const subRealEvent = async () => {
  if (realEventState.value.subscribe) {
    return;
  }
  refreshLoading.value = true;
  try {
    // 订阅事件
    const res = await realEventApi.subRealEvent([globalReport!.currReportType]);
    if (res.code != 0) {
      Message.error(res.msg);
    } else {
      realEventState.value.subscribe = true;
      realEventState.value.type = globalReport!.currReportType;
    }
  } finally {
    refreshLoading.value = false;
  }
};
const unSubRealEvent = async () => {
  if (!realEventState.value.subscribe) {
    return;
  }
  refreshLoading.value = true;
  try {
    // 取消订阅
    const res = await realEventApi.unSubRealEvent({ type: [realEventState.value.type] });
    if (res.code != 0) {
      Message.error(res.msg);
    } else {
      realEventState.value.subscribe = false;
      realEventState.value.type = "";
    }
  } finally {
    refreshLoading.value = false;
  }
};
onUnmounted(() => {
  unSubRealEvent();
});
</script>
<style scoped lang="scss">
.report-page {
  width: 100%;
  margin-top: 5px;
  .el-row {
    div {
      font-size: 14px;
      :deep(.el-date-editor) {
        width: 340px;
      }
    }
  }
  .button-group {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    margin: 8px 0;
    .search-page {
      font-size: 14px;
    }
    .his-var {
      font-size: 14px;
    }
  }
  .report-table {
    height: 100%;
    overflow-y: auto;
    scrollbar-width: none;
  }
}
.header {
  margin-bottom: 5px;
}
</style>
