export default {
  // 通用操作
  confirm: "Confirm",
  cancel: "Cancel",
  save: "Save",
  delete: "Delete",
  edit: "Edit",
  add: "Add",
  search: "Search",
  reset: "Reset",
  export: "Export",
  import: "Import",
  upload: "Upload",
  download: "Download",
  preview: "Preview",
  print: "Print",
  refresh: "Refresh",
  back: "Back",
  next: "Next",
  submit: "Submit",
  loading: "Loading...",
  success: "Success",
  error: "Error",
  warning: "Warning",
  info: "Info",
  index: "Index",
  title: "Title",
  operation: "Operation",
  execute: "Execute",
  clear: "Clear",
  moveUp: "Move Up",
  moveDown: "Move Down",

  // 状态
  status: {
    active: "Active",
    inactive: "Inactive",
    enabled: "Enabled",
    disabled: "Disabled",
    online: "Online",
    offline: "Offline",
    pending: "Pending",
    completed: "Completed",
    failed: "Failed"
  },

  // 时间相关
  time: {
    today: "Today",
    yesterday: "Yesterday",
    thisWeek: "This Week",
    lastWeek: "Last Week",
    thisMonth: "This Month",
    lastMonth: "Last Month",
    custom: "Custom Range"
  },

  // 分页
  pagination: {
    total: "Total",
    items: "Items",
    page: "Page",
    perPage: "Per Page",
    showing: "Showing",
    to: "To",
    of: "Of"
  },

  // 表单验证
  validation: {
    required: "This field is required",
    email: "Please enter a valid email address",
    phone: "Please enter a valid phone number",
    number: "Please enter a valid number",
    integer: "Please enter a valid integer",
    min: "Minimum value is {min}",
    max: "Maximum value is {max}",
    length: "Length must be {length}",
    minLength: "Minimum length is {min}",
    maxLength: "Maximum length is {max}"
  },

  // 消息提示
  message: {
    saveSuccess: "Save successful",
    deleteSuccess: "Delete successful",
    updateSuccess: "Update successful",
    operationSuccess: "Operation successful",
    operationFailed: "Operation failed",
    confirmDelete: "Are you sure to delete?",
    noData: "No data",
    loading: "Loading...",
    networkError: "Network error, please try again",
    copySuccess: "Copy successful"
  },

  // Language related
  languageSyncWarning: "Language sync to backend failed, but frontend language changed successfully",

  // 自定义文件选择器
  customFileSelector: {
    title: "Select Files and Folders",
    searchPlaceholder: "Search files or folders...",
    selectedItems: "Selected Items",
    clearAll: "Clear All",
    noItemsSelected: "No items selected",
    cancel: "Cancel",
    confirm: "Confirm",
    loading: "Loading...",
    error: {
      loadFailed: "Load failed",
      accessDenied: "Access denied",
      notFound: "Path not found"
    }
  },

  // Test related
  test: {
    languageSwitch: {
      title: "Language Switch Test",
      progressDialog: "Progress Dialog Test",
      showProgress: "Show Progress Dialog",
      temperatureConverter: "Temperature Converter Test",
      temperatureDesc: "The following temperature unit names should automatically update after language switch:",
      reportNames: "Report Names Test",
      reportDesc: "The following report-related names should automatically update after language switch:",
      autoRefresh: "Auto Refresh",
      showHidden: "Show Hidden Items",
      instructions: "Test Instructions",
      step1: "Click the language switch button in the top right corner",
      step2: "Select a different language (such as English, Spanish, French)",
      step3: "Observe whether the text on the page immediately updates to the new language"
    }
  }
};
