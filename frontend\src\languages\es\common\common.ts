export default {
  // 通用操作
  confirm: "Confirmar",
  cancel: "Cancelar",
  save: "Guardar",
  delete: "Eliminar",
  edit: "Editar",
  add: "Agregar",
  search: "Buscar",
  reset: "Restablecer",
  export: "Exportar",
  import: "Importar",
  upload: "Subir",
  download: "Descargar",
  preview: "Vista previa",
  print: "Imprimir",
  refresh: "Actualizar",
  back: "Atrás",
  next: "Siguiente",
  submit: "Enviar",
  loading: "Cargando...",
  success: "Éxito",
  error: "Error",
  warning: "Advertencia",
  info: "Información",
  index: "Índice",
  title: "Títu<PERSON>",
  operation: "Operación",
  execute: "Ejecutar",
  clear: "Limpiar",
  moveUp: "Subir",
  moveDown: "Bajar",

  // 状态
  status: {
    active: "Activado",
    inactive: "No activado",
    enabled: "Habilitado",
    disabled: "Deshabilitado",
    online: "En línea",
    offline: "Fuera de línea",
    pending: "Pendiente",
    completed: "Completado",
    failed: "Fallido"
  },

  // 时间相关
  time: {
    today: "Hoy",
    yesterday: "Ayer",
    thisWeek: "Esta semana",
    lastWeek: "La semana pasada",
    thisMonth: "Este mes",
    lastMonth: "El mes pasado",
    custom: "Rango personalizado"
  },

  // 分页
  pagination: {
    total: "Total",
    items: "Elementos",
    page: "Página",
    perPage: "Por página",
    showing: "Mostrando",
    to: "A",
    of: "De"
  },

  // 表单验证
  validation: {
    required: "Este campo es obligatorio",
    email: "Por favor, ingrese una dirección de correo electrónico válida",
    phone: "Por favor, ingrese un número de teléfono válido",
    number: "Por favor, ingrese un número válido",
    integer: "Por favor, ingrese un número entero válido",
    min: "El valor mínimo es {min}",
    max: "El valor máximo es {max}",
    length: "La longitud debe ser {length}",
    minLength: "La longitud mínima es {min}",
    maxLength: "La longitud máxima es {max}"
  },

  // 消息提示
  message: {
    saveSuccess: "Guardado exitosamente",
    deleteSuccess: "Eliminado exitosamente",
    updateSuccess: "Actualizado exitosamente",
    operationSuccess: "Operación exitosa",
    operationFailed: "Operación fallida",
    confirmDelete: "¿Está seguro de que desea eliminar?",
    noData: "Sin datos",
    loading: "Cargando...",
    networkError: "Error de red, por favor intente de nuevo",
    copySuccess: "Copiado exitosamente"
  },

  // 自定义文件选择器
  customFileSelector: {
    title: "Seleccionar archivos y carpetas",
    searchPlaceholder: "Buscar archivos o carpetas...",
    selectedItems: "Elementos seleccionados",
    clearAll: "Limpiar todo",
    noItemsSelected: "Ningún elemento seleccionado",
    cancel: "Cancelar",
    confirm: "Confirmar",
    loading: "Cargando...",
    error: {
      loadFailed: "Error al cargar",
      accessDenied: "Acceso denegado",
      notFound: "Ruta no encontrada"
    }
  },

  // 语言同步警告
  languageSyncWarning: "Error al sincronizar idioma con el backend, pero el idioma del frontend se cambió exitosamente",

  // 测试相关
  test: {
    languageSwitch: {
      title: "Prueba de cambio de idioma",
      progressDialog: "Prueba de diálogo de progreso",
      showProgress: "Mostrar diálogo de progreso",
      temperatureConverter: "Prueba de conversor de temperatura",
      temperatureDesc: "Los siguientes nombres de unidades de temperatura deberían actualizarse automáticamente después del cambio de idioma:",
      reportNames: "Prueba de nombres de informes",
      reportDesc: "Los siguientes nombres relacionados con informes deberían actualizarse automáticamente después del cambio de idioma:",
      autoRefresh: "Actualización automática",
      showHidden: "Mostrar elementos ocultos",
      instructions: "Instrucciones de prueba",
      step1: "Haga clic en el botón de cambio de idioma en la esquina superior derecha",
      step2: "Seleccione un idioma diferente (como inglés, español, francés)",
      step3: "Observe si el texto en la página se actualiza inmediatamente al nuevo idioma"
    }
  }
};
