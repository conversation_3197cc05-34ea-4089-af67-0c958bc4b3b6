export default {
  common: {
    date: "Fecha",
    search: "Buscar",
    save: "Guardar",
    clear: "Limpiar",
    loading: "Cargando...",
    reportNo: "Número de informe",
    time: "Tiempo",
    description: "Descripción",
    progress: "Progreso",
    selectDateRange: "Por favor seleccione el rango de fecha",
    noData: "Sin datos",
    saveSuccess: "Guardado exitosamente",
    saveFailed: "Error al guardar"
  },
  date: "Fecha",
  search: "Buscar",
  filter: "Filtrar",
  save: "Guardar",
  clearList: "Limpiar lista",
  loading: "Cargando...",
  reportNumber: "Número de informe",
  time: "Tiempo",
  description: "Descripción",
  progress: "Progreso",
  loadingText: "Cargando...",
  querying: "Buscando...",
  selectCompleteTimeRange: "Por favor seleccione el rango de fecha completo",
  noDataToSave: "No hay datos para guardar",
  saveSuccess: "Guardado exitosamente",
  saveReport: "Guardar informe",
  fileUploading: "Subiendo archivo",
  fileUploadComplete: "Archivo subido completamente",
  autoRefresh: "Actualización automática",
  showHiddenItems: "Mostrar elementos ocultos",
  name: "Nombre",
  operationAddress: "Dirección de operación",
  operationParams: "Parámetros de operación",
  value: "Valor",
  step: "Paso",
  source: "Fuente",
  sourceType: "Tipo de fuente",
  result: "Resultado",
  searchType: "Tipo de búsqueda",
  total: "Total {num} registros",
  sameSearch: "Búsqueda de contenido similar",
  sameFilter: "Filtrado de contenido similar",
  showHideTime: "Mostrar/Ocultar columna de tiempo",
  selectRowToOperate: "Por favor seleccione la fila a operar",
  trip: {
    autoRefresh: "Actualización automática"
  },
  operate: {
    name: "Nombre",
    operateAddress: "Dirección de operación",
    operateParam: "Parámetro de operación",
    value: "Valor",
    step: "Paso",
    source: "Fuente",
    sourceType: "Tipo de fuente",
    result: "Resultado"
  },
  group: {
    uploadWave: "Subir onda",
    searchHistory: "Buscar historial",
    saveResult: "Guardar resultado",
    clearContent: "Limpiar contenido",
    contextMenu: {
      uploadWave: "Subir onda",
      getHistoryReport: "Obtener informe histórico",
      saveResult: "Guardar resultado",
      clearContent: "Limpiar contenido"
    },
    date: "Fecha",
    search: "Buscar",
    save: "Guardar",
    clearList: "Limpiar lista",
    loading: "Cargando...",
    table: {
      reportId: "ID de informe",
      time: "Tiempo",
      description: "Descripción"
    },
    progress: {
      title: "Progreso",
      searching: "Buscando {type}",
      loading: "Cargando..."
    },
    refresh: {
      start: "Iniciar actualización",
      stop: "Detener actualización"
    },
    hiddenItems: {
      show: "Mostrar elementos ocultos"
    },
    messages: {
      noFileToUpload: "No hay archivo para subir",
      selectDateRange: "Por favor seleccione el rango de fecha",
      noDataToSave: "No hay datos para guardar",
      saveReport: "Guardar informe",
      saveSuccess: "Guardado exitosamente"
    }
  },
  entryID: "Índice",
  module: "Nombre del módulo",
  msg: "Contenido",
  level: "Nivel",
  type: "Tipo",
  origin: "Origen",
  user: "Nombre de usuario",
  pleaseSelectSavePath: "Por favor seleccione la ruta de guardado...",
  saveFailed: "Guardado fallido",
  exportLogSuccess: "Exportación exitosa: {path}",
  exportLogFailed: "Exportación fallida: {msg}"
};
