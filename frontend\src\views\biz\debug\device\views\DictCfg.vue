<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :pagination="false"
      :init-param="initParam"
      :request-auto="false"
      :data="tableData"
      :loading="loading"
      highlight-current-row
      table-key="dictCfg"
      @search="getDictInfo"
      row-key="abbr"
    >
      <template #tableHeader="">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-button type="primary" plain :icon="Refresh" @click="refreshDict">{{ t("device.dict.refresh") }}</el-button>
          <el-button type="primary" :icon="EditPen" @click="confirmDict">{{ t("device.dict.confirm") }}</el-button>
          <el-button type="success" :icon="Upload" @click="importDict">{{ t("device.dict.import") }}</el-button>
          <el-button type="success" :icon="Download" @click="exportDict">{{ t("device.dict.export") }}</el-button>
        </div>
      </template>
      <template #operation="">
        <el-button type="primary" link :icon="EditPen" @click="confirmDict">{{ t("device.dict.confirm") }}</el-button>
      </template>
      <template #expand="scope">
        {{ scope.row }}
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="useProTable">
import { ref, reactive, onMounted, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDebugStore } from "@/stores/modules/debug";
import { dictConfigApi } from "@/api/modules/biz/debug";
const { t } = useI18n();
const { debugIndex } = useDebugStore();
import { Upload, EditPen, Download, Refresh } from "@element-plus/icons-vue";
import { DictInfo, ProjectDictEntry, ProjectDictRequestData, SetProjectDictRequestData } from "@/api/interface/biz/debug/dictinfo";
// ProTable 实例
const proTable = ref<ProTableInstance>();
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });
const tableData = ref<ProjectDictEntry[]>([]);
// 加载状态
const loading = ref(false);
// 修改的词条数据
const modifiedEntries = ref<Map<string, ProjectDictEntry>>(new Map());
const getDictInfo = async () => {
  loading.value = true;
  try {
    const searchParam = proTable.value?.searchParam || {};
    const requestData: ProjectDictRequestData = {
      abbrAfter: searchParam.abbr || "",
      autoReadAfter: true
    };

    const result = await dictConfigApi.getProjectDict(requestData);

    if (result.isSuccess()) {
      tableData.value = result.data.dict || [];
      ElMessage.success(t("device.dict.loadSuccess"));
    } else {
      ElMessage.error(result.msg || t("device.dict.loadFailed"));
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取词条失败:", error);
    ElMessage.error(t("device.dict.loadFailed"));
    tableData.value = [];
  } finally {
    loading.value = false;
    proTable.value?.refresh();
  }
};
const confirmDict = async () => {
  if (modifiedEntries.value.size === 0) {
    ElMessage.warning(t("device.dict.noChanges"));
    return;
  }

  try {
    await ElMessageBox.confirm(t("device.dict.confirmMessage"), t("device.dict.confirm"), {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning"
    });

    loading.value = true;
    const entrysToUpdate = Array.from(modifiedEntries.value.values());

    const requestData: SetProjectDictRequestData = {
      entrys: entrysToUpdate,
      batchSize: 80
    };

    const result = await dictConfigApi.setProjectDict(requestData);

    if (result.isSuccess()) {
      if (result.data.success) {
        ElMessage.success(t("device.dict.saveSuccess"));
        modifiedEntries.value.clear();
        // 重新加载数据
        await getDictInfo();
      } else {
        // 处理部分失败的情况
        const errorMessages = result.data.errorData.map(error => `位置 ${error.position}: 错误码 ${error.serviceError}`).join(", ");
        ElMessage.error(`${t("device.dict.partialFailed")}: ${errorMessages}`);
      }
    } else {
      ElMessage.error(result.msg || t("device.dict.saveFailed"));
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("保存词条失败:", error);
      ElMessage.error(t("device.dict.saveFailed"));
    }
  } finally {
    loading.value = false;
  }
};

const importDict = async () => {
  ElMessage.info(t("device.dict.importLog"));
  // TODO: 实现导入功能
};

const exportDict = async () => {
  ElMessage.info(t("device.dict.exportLog"));
  // TODO: 实现导出功能
};

const refreshDict = async () => {
  modifiedEntries.value.clear();
  await getDictInfo();
};
onMounted(() => {
  getDictInfo();
});

// 处理单元格编辑
const handleCellEdit = (row: ProjectDictEntry, prop: string, value: string) => {
  const updatedEntry = { ...row, [prop]: value };
  modifiedEntries.value.set(row.abbr, updatedEntry);

  // 更新表格数据
  const index = tableData.value.findIndex(item => item.abbr === row.abbr);
  if (index !== -1) {
    tableData.value[index] = updatedEntry;
  }
};

// 表格配置项
const columns = reactive<ColumnProps<ProjectDictEntry>[]>([
  { type: "index", label: t("device.dict.sequence"), fixed: "left", width: 60 },
  {
    prop: "abbr",
    label: t("device.dict.shortAddress"),
    width: 200,
    search: {
      el: "input",
      tooltip: t("device.dict.shortAddressTooltip"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "cnDesc",
    label: t("device.dict.chinese"),
    render: scope => {
      const isModified = modifiedEntries.value.has(scope.row.abbr);
      return (
        <el-input
          v-model={scope.row.cnDesc}
          placeholder={t("device.dict.chinese")}
          class={isModified ? "modified-cell" : ""}
          onBlur={() => handleCellEdit(scope.row, "cnDesc", scope.row.cnDesc)}
        />
      );
    }
  },
  {
    prop: "enDesc",
    label: t("device.dict.english"),
    render: scope => {
      const isModified = modifiedEntries.value.has(scope.row.abbr);
      return (
        <el-input
          v-model={scope.row.enDesc}
          placeholder={t("device.dict.english")}
          class={isModified ? "modified-cell" : ""}
          onBlur={() => handleCellEdit(scope.row, "enDesc", scope.row.enDesc)}
        />
      );
    }
  },
  {
    prop: "esDesc",
    label: t("device.dict.spanish"),
    render: scope => {
      const isModified = modifiedEntries.value.has(scope.row.abbr);
      return (
        <el-input
          v-model={scope.row.esDesc}
          placeholder={t("device.dict.spanish")}
          class={isModified ? "modified-cell" : ""}
          onBlur={() => handleCellEdit(scope.row, "esDesc", scope.row.esDesc)}
        />
      );
    }
  },
  {
    prop: "ruDesc",
    label: t("device.dict.russian"),
    render: scope => {
      const isModified = modifiedEntries.value.has(scope.row.abbr);
      return (
        <el-input
          v-model={scope.row.ruDesc}
          placeholder={t("device.dict.russian")}
          class={isModified ? "modified-cell" : ""}
          onBlur={() => handleCellEdit(scope.row, "ruDesc", scope.row.ruDesc)}
        />
      );
    }
  },
  { prop: "operation", label: t("device.dict.operation"), fixed: "right", width: 200 }
]);

watch(
  debugIndex.compData,
  newValue => {
    console.log(t("device.dict.newValueLog"), newValue);
    if (newValue) {
      proTable.value?.reset();
      getDictInfo();
    }
  },
  { deep: true }
);
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 8px;
}
:deep(.modified-cell) {
  background-color: #f0f9ff !important;
  border-color: #409eff !important;
}
:deep(.modified-cell:focus) {
  border-color: #409eff !important;
  box-shadow: 0 0 0 2px rgb(64 158 255 / 20%) !important;
}
</style>
