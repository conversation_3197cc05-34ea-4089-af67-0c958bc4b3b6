<template>
  <el-dialog
    v-model="progressDialog.show"
    width="40%"
    class="hover"
    :align-center="true"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :show-close="false"
    draggable
    :title="t('device.progress.title')"
  >
    <div style="height: 120px; padding: 15px">
      <el-progress
        :duration="3"
        :percentage="progressDialog.percentage"
        :text-inside="false"
        striped
        :stroke-width="10"
        :indeterminate="progressDialog.indeterminate"
        style="margin-top: 40px"
      >
        <span>{{ displayProgressText }}</span>
      </el-progress>
    </div>
    <template #header>
      <div class="dialog-header">
        <el-icon><ChromeFilled /></el-icon>
        <span>{{ t("device.progress.title") }}</span>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ChromeFilled } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
import { computed } from "vue";

const { t } = useI18n();

// 使用响应式的默认进度文本
const defaultProgressText = computed(() => t("device.progress.executing"));

const progressDialog = ref({
  show: false,
  percentage: 75,
  progressText: "", // 空字符串表示使用默认文本
  indeterminate: true
});

// 计算显示的进度文本，如果没有自定义文本则使用默认的响应式文本
const displayProgressText = computed(() => {
  return progressDialog.value.progressText || defaultProgressText.value;
});
const show = (): void => {
  progressDialog.value.show = true;
};
const hide = (): void => {
  progressDialog.value.show = false;
};
const setProgress = (percentage?: number, progressText?: string, indeterminate?: boolean): void => {
  if (typeof percentage === "number") progressDialog.value.percentage = percentage;
  if (typeof progressText === "string") progressDialog.value.progressText = progressText;
  if (typeof indeterminate === "boolean") progressDialog.value.indeterminate = indeterminate;
};
defineExpose({
  show,
  hide,
  setProgress
});
</script>
<style lang="css" scoped>
.dialog-header {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
