/**
 * 日志信息 - 中文
 */
export default {
  reportController: {
    getCommonReportListEntry: "getCommonReportList 入参",
    getCommonReportListReturn: "getCommonReportList 返回",
    getCommonReportListError: "getCommonReportList 异常",
    cancelUploadStart: "取消录波文件上招开始",
    cancelUploadError: "取消录波文件上招异常",
    openWaveFileStart: "打开录波文件开始",
    openWaveFileError: "打开录波文件异常",
    getGroupReportStart: "获取整组报告开始",
    getGroupReportError: "获取整组报告异常",
    getOperateReportStart: "获取操作报告开始",
    getOperateReportError: "获取操作报告异常",
    getAuditReportStart: "获取审计报告开始",
    getAuditReportError: "获取审计报告异常",
    exportCommonReportStart: "导出通用报告开始",
    exportCommonReportError: "导出通用报告异常",
    clearReportStart: "清除报告开始",
    clearReportError: "清除报告异常",
    refreshReportStart: "刷新通用报告开始",
    refreshReportError: "刷新通用报告异常",
    refreshGroupReportStart: "刷新整组报告开始",
    refreshGroupReportError: "刷新整组报告异常",
    refreshOperateReportStart: "刷新操作报告开始",
    refreshOperateReportError: "刷新操作报告异常",
    refreshTripReportStart: "刷新动作报告开始",
    refreshTripReportError: "刷新动作报告异常",
    uploadWaveStart: "录波文件上招开始",
    uploadWaveError: "录波文件上招异常"
  },
  configureService: {
    getConfigureListError: "获取组态列表异常",
    loadConfigureError: "加载组态异常"
  },
  // 控制器日志
  configureController: {
    initialized: "控制器初始化完成",
    getConfigureListStart: "开始获取组态列表",
    getConfigureListError: "获取组态列表时发生错误",
    addConfigureStart: "开始新增组态",
    addConfigureError: "新增组态时发生错误",
    renameConfigureStart: "开始重命名组态",
    renameConfigureError: "重命名组态时发生错误",
    removeConfigureStart: "开始删除组态",
    removeConfigureError: "删除组态时发生错误",
    saveConfigureStart: "开始保存组态",
    saveConfigureError: "保存组态时发生错误",
    loadConfigureStart: "开始加载组态",
    loadConfigureError: "加载组态时发生错误",
    openConfigureDirStart: "开始打开组态文件夹",
    openConfigureDirError: "打开组态文件夹时发生错误"
  },
  deviceConnectController: {
    initialized: "控制器初始化完成",
    connectDeviceStart: "开始连接设备，连接参数",
    connectDeviceCallService: "调用服务层连接设备",
    connectDeviceServiceResult: "服务层返回结果",
    connectDeviceSuccess: "设备连接成功",
    connectDeviceGetError: "获取错误信息，日志输出",
    connectDeviceFailed: "连接设备失败，错误信息",
    connectDeviceException: "捕获异常，日志输出",
    connectDeviceExceptionDetail: "连接设备异常",
    disconnectDeviceStart: "开始断开设备连接，设备ID",
    disconnectDeviceCheckStatus: "检查设备连接状态",
    disconnectDeviceAlready: "设备已断开连接，设备ID",
    disconnectDeviceCallService: "调用服务层断开设备连接",
    disconnectDeviceResult: "断开连接结果",
    disconnectDeviceSuccess: "设备断开连接成功，设备ID",
    disconnectDeviceException: "断开设备连接异常，设备ID",
    disconnectDeviceFailed: "断开设备连接失败，设备ID"
  },
  deviceOperateController: {
    initialized: "控制器初始化完成",
    addDeviceStart: "开始新增设备配置，请求参数",
    addDeviceCallService: "调用服务层新增设备配置",
    addDeviceSuccess: "新增设备配置成功，结果",
    addDeviceException: "新增设备配置异常",
    updateDeviceStart: "开始修改设备配置，请求参数",
    updateDeviceCallService: "调用服务层修改设备配置",
    updateDeviceSuccess: "修改设备配置成功，结果",
    updateDeviceException: "修改设备配置异常",
    removeDeviceStart: "开始删除设备配置，请求参数",
    removeDeviceCallService: "调用服务层删除设备配置，设备ID",
    removeDeviceResult: "删除设备配置结果",
    removeDeviceSuccess: "删除设备配置成功，设备ID",
    removeDeviceFailed: "删除设备配置失败，设备ID",
    removeDeviceException: "删除设备配置异常，设备ID",
    getDeviceListStart: "开始获取设备配置列表",
    getDeviceListCallService: "调用服务层获取设备配置列表",
    getDeviceListSuccess: "成功获取设备配置列表，设备数量",
    getDeviceListException: "获取设备配置列表异常"
  },
  paramService: {
    getDiffParamComplete: "比对完成，差异组数",
    getAllDiffParamError: "getAllDiffParam error",
    getParamInfoEntry: "getParamInfo 入参",
    getParamInfoReturn: "getParamInfo 返回",
    getParamInfoError: "getParamInfo 异常",
    startGetParamInfo: "开始获取参数定值，分页",
    getAllParamInfoStart: "开始获取所有参数定值",
    getAllParamInfoSuccess: "获取所有参数定值成功，总数",
    modifyParamStart: "开始修改参数定值",
    validateParam: "校验参数项",
    validateFailed: "参数校验失败",
    validatePassed: "参数校验通过，准备下发",
    setTimeout: "设置超时时间",
    sendResponse: "下发响应",
    modifySuccess: "修改成功",
    sendFailed: "下发失败",
    businessError: "业务错误",
    getAllDiffParamStart: "开始批量比对参数",
    excelParseFailed: "Excel解析失败",
    csvParseFailed: "CSV解析失败",
    xmlParseFailed: "XML解析失败",
    fileParseComplete: "文件解析完成",
    getDiffParamStart: "开始单组比对参数",
    diffComplete: "比对完成，差异项数",
    importParamStart: "开始导入参数定值",
    paramReady: "参数准备下发",
    importSuccess: "导入成功",
    exportAllParamStart: "开始导出所有参数定值",
    exportComplete: "导出完成",
    exportParamStart: "开始导出分组参数",
    getGroupItemsStart: "获取分组参数项",
    getParamValueFailed: "获取参数值失败",
    getGroupItemsComplete: "获取完成，参数项数",
    getAllGroupItemsStart: "获取所有分组参数项",
    groupParamCount: "分组：{group}，参数项数：{count}",
    getCurrentRunAreaStart: "获取当前运行区",
    getCurrentRunAreaSuccess: "获取成功",
    getCurrentRunAreaFailed: "获取失败",
    selectRunAreaStart: "选择定值区",
    runAreaEmpty: "定值区不能为空",
    selectRunAreaSuccess: "选择成功",
    selectRunAreaFailed: "选择失败"
  },
  debugInfoMenuService: {
    initialized: "初始化完成",
    getDebugInfoEntry: "getDebugInfo 入参",
    getTreeMenuError: "getTreeMenu 异常",
    getTreeMenuComplete: "处理完成，菜单数量"
  },
  deviceInfoController: {
    initialized: "控制器初始化完成",
    getDeviceInfoStart: "开始获取设备信息，请求参数",
    getDeviceInfoCheckConnection: "检查设备连接状态",
    getDeviceInfoNotConnected: "设备未连接，无法获取设备信息",
    getDeviceInfoConnected: "设备已连接，调用服务层获取设备信息",
    getDeviceInfoSuccess: "成功获取设备信息，结果数量",
    getDeviceInfoException: "获取设备信息异常",
    exportDeviceInfoStart: "开始导出设备信息，请求参数",
    exportDeviceInfoCheckConnection: "检查设备连接状态",
    exportDeviceInfoNotConnected: "设备未连接，无法导出设备信息",
    exportDeviceInfoValidateParams: "验证导出参数，数据数量",
    exportDeviceInfoPath: "导出路径",
    exportDeviceInfoEmptyData: "导出数据为空",
    exportDeviceInfoEmptyPath: "导出文件路径为空",
    exportDeviceInfoFileExtension: "文件扩展名",
    exportDeviceInfoUnsupportedFormat: "不支持的文件格式",
    exportDeviceInfoDirPath: "导出目录路径",
    exportDeviceInfoCreateDir: "创建导出目录",
    exportDeviceInfoCreateDirFailed: "创建导出目录失败",
    exportDeviceInfoCallService: "调用服务层导出设备信息",
    exportDeviceInfoSuccess: "设备信息导出成功，导出路径",
    exportDeviceInfoException: "导出设备信息异常"
  },
  variableController: {
    getVariableEntry: "getVariable 入参",
    getVariableReturn: "获取变量方法返回日志",
    getVariableException: "获取变量方法异常日志",
    addVariableEntry: "addVariable 入参",
    addVariableReturn: "添加变量方法返回日志",
    addVariableException: "添加变量方法异常日志",
    modifyVariableEntry: "modifyVariable 入参",
    modifyVariableReturn: "修改变量方法返回日志",
    modifyVariableException: "修改变量方法异常日志",
    deleteVariableEntry: "deleteVariable 入参",
    deleteVariableReturn: "删除变量方法返回日志",
    deleteVariableException: "删除变量方法异常日志",
    exportVariableEntry: "exportVariable 入参",
    exportVariableEmptyPath: "导出路径不能为空",
    exportVariableReturn: "导出变量方法返回日志",
    exportVariableException: "导出变量方法异常日志",
    importVariableEntry: "importVariable 入参",
    importVariableEmptyPath: "导入路径不能为空",
    importVariableReturn: "导入变量方法返回日志",
    importVariableException: "导入变量方法异常日志"
  },
  paramController: {
    initialized: "控制器初始化完成",
    getParamStart: "开始获取装置定值，请求参数",
    getParamNotConnected: "设备未连接，无法获取装置定值",
    getParamConnected: "设备已连接，调用服务层获取装置定值",
    getParamSuccess: "成功获取装置定值，结果数量",
    getParamException: "获取装置定值异常",
    getAllParamStart: "开始获取所有装置定值，请求参数",
    getAllParamNotConnected: "设备未连接，无法获取所有装置定值",
    getAllParamConnected: "设备已连接，调用服务层获取所有装置定值",
    getAllParamSuccess: "成功获取所有装置定值，结果数量",
    getAllParamException: "获取所有装置定值异常",
    confirmParamStart: "开始修改装置定值，请求参数",
    confirmParamNotConnected: "设备未连接，无法修改装置定值",
    confirmParamConnected: "设备已连接，调用服务层修改装置定值",
    confirmParamSuccess: "修改装置定值成功，结果",
    confirmParamException: "修改装置定值异常",
    getDiffParamStart: "开始获取定值差异，请求参数",
    getDiffParamNotConnected: "设备未连接，无法获取定值差异",
    getDiffParamPath: "导入路径",
    getDiffParamEmptyPath: "导入路径为空",
    getDiffParamConnected: "设备已连接，调用服务层获取定值差异",
    getDiffParamSuccess: "成功获取定值差异，结果",
    getDiffParamException: "获取定值差异异常",
    getAllDiffParamStart: "开始获取所有定值差异，请求参数",
    getAllDiffParamNotConnected: "设备未连接，无法获取所有定值差异",
    getAllDiffParamPath: "导入路径",
    getAllDiffParamEmptyPath: "导入路径为空",
    getAllDiffParamConnected: "设备已连接，调用服务层获取所有定值差异",
    getAllDiffParamSuccess: "成功获取所有定值差异，结果",
    getAllDiffParamException: "获取所有定值差异异常",
    importParamStart: "开始导入装置定值，请求参数",
    importParamNotConnected: "设备未连接，无法导入装置定值",
    importParamConnected: "设备已连接，调用服务层导入装置定值",
    importParamSuccess: "导入装置定值成功，结果",
    importParamException: "导入装置定值异常",
    exportParamStart: "开始导出装置定值，请求参数",
    exportParamNotConnected: "设备未连接，无法导出装置定值",
    exportParamPath: "导出路径",
    exportParamEmptyPath: "导出路径为空",
    exportParamConnected: "设备已连接，调用服务层导出装置定值",
    exportParamSuccess: "导出装置定值成功，结果",
    exportParamException: "导出装置定值异常",
    exportAllParamStart: "开始导出所有装置定值，请求参数",
    exportAllParamNotConnected: "设备未连接，无法导出所有装置定值",
    exportAllParamPath: "导出路径",
    exportAllParamEmptyPath: "导出路径为空",
    exportAllParamConnected: "设备已连接，调用服务层导出所有装置定值",
    exportAllParamSuccess: "导出所有装置定值成功，结果",
    exportAllParamException: "导出所有装置定值异常",
    getCurrentRunAreaStart: "开始获取当前运行区，请求参数",
    getCurrentRunAreaNotConnected: "设备未连接，无法获取当前运行区",
    getCurrentRunAreaConnected: "设备已连接，调用服务层获取当前运行区",
    getCurrentRunAreaSuccess: "成功获取当前运行区，结果",
    getCurrentRunAreaException: "获取当前运行区异常",
    selectRunAreaStart: "开始选择定值区，请求参数",
    selectRunAreaNotConnected: "设备未连接，无法选择定值区",
    selectRunAreaConnected: "设备已连接，调用服务层选择定值区",
    selectRunAreaSuccess: "成功选择定值区，结果",
    selectRunAreaException: "选择定值区异常"
  },
  remoteControlController: {
    ykSelectEntry: "遥控选择方法入口日志",
    ykSelectReturn: "遥控选择方法返回日志",
    ykSelectException: "遥控选择方法异常日志"
  },
  baseController: {
    getDeviceInfoStart: "开始获取设备信息，设备ID",
    getDeviceInfoSuccess: "成功获取设备信息，设备ID",
    getDeviceInfoNotFound: "未找到设备信息，设备ID",
    getDeviceInfoFailed: "获取设备信息失败，设备ID"
  },
  debugInfoMenuController: {
    initialized: "控制器初始化完成",
    getDeviceMenuTreeStart: "开始获取设备菜单树，设备ID",
    getDeviceMenuTreeCheckConnection: "检查设备连接状态，设备ID",
    getDeviceMenuTreeNotConnected: "设备未连接，无法获取菜单树，设备ID",
    getDeviceMenuTreeConnected: "设备已连接，调用服务层获取菜单树",
    getDeviceMenuTreeSuccess: "成功获取设备菜单树，菜单项数量",
    getDeviceMenuTreeEmpty: "菜单树为空，设备ID",
    getDeviceMenuTreeException: "获取设备菜单树异常，设备ID",
    getTreeItemByNameStart: "开始获取菜单项，请求参数",
    getTreeItemByNameCheckConnection: "检查设备连接状态，设备ID",
    getTreeItemByNameNotConnected: "设备未连接，无法获取菜单项，设备ID",
    getTreeItemByNameConnected: "设备已连接，调用服务层获取菜单项",
    getTreeItemByNameSuccess: "成功获取菜单项，结果",
    getTreeItemByNameNotFound: "未找到菜单项，设备ID",
    getTreeItemByNameException: "获取菜单项异常，设备ID",
    getGroupInfoListStart: "开始获取分组信息，请求参数",
    getGroupInfoListCheckConnection: "检查设备连接状态，设备ID",
    getGroupInfoListNotConnected: "设备未连接，无法获取分组信息，设备ID",
    getGroupInfoListConnected: "设备已连接，调用服务层获取分组信息，设备ID",
    getGroupInfoListSuccess: "成功获取分组信息，结果",
    getGroupInfoListNotFound: "未找到分组信息，设备ID",
    getGroupInfoListException: "获取分组信息异常，设备ID"
  },
  deviceFileController: {
    initialized: "控制器初始化完成",
    getDeviceFileStart: "开始获取设备文件目录，请求参数",
    getDeviceFileCheckConnection: "检查设备连接状态",
    getDeviceFileNotConnected: "设备未连接，无法获取文件目录",
    getDeviceFileConnected: "设备已连接，调用服务层获取文件目录",
    getDeviceFileSuccess: "成功获取设备文件目录",
    getDeviceFileException: "获取设备文件目录异常",
    uploadDeviceFileStart: "开始上传设备文件，请求参数",
    uploadDeviceFileCheckConnection: "检查设备连接状态",
    uploadDeviceFileNotConnected: "设备未连接，无法上传文件",
    uploadDeviceFileConnected: "设备已连接，调用服务层上传文件",
    uploadDeviceFileSuccess: "文件上传成功，结果",
    uploadDeviceFileException: "文件上传异常",
    cancelUploadDeviceFileStart: "开始取消文件上传，请求参数",
    cancelUploadDeviceFileCheckConnection: "检查设备连接状态",
    cancelUploadDeviceFileNotConnected: "设备未连接，无法取消上传",
    cancelUploadDeviceFileConnected: "设备已连接，调用服务层取消上传",
    cancelUploadDeviceFileSuccess: "取消上传成功，结果",
    cancelUploadDeviceFileException: "取消上传异常",
    downloadDeviceFileStart: "开始下载设备文件，请求参数",
    downloadDeviceFileCheckConnection: "检查设备连接状态",
    downloadDeviceFileNotConnected: "设备未连接，无法下载文件",
    downloadDeviceFileConnected: "设备已连接，调用服务层下载文件",
    downloadDeviceFileSuccess: "文件下载成功，结果",
    downloadDeviceFileException: "文件下载异常",
    cancelDownloadDeviceFileStart: "开始取消文件下载，请求参数",
    cancelDownloadDeviceFileCheckConnection: "检查设备连接状态",
    cancelDownloadDeviceFileNotConnected: "设备未连接，无法取消下载",
    cancelDownloadDeviceFileConnected: "设备已连接，调用服务层取消下载",
    cancelDownloadDeviceFileSuccess: "取消下载成功，结果",
    cancelDownloadDeviceFileException: "取消下载异常",
    importDownloadDeviceFileStart: "开始导入下载的设备文件，请求参数",
    importDownloadDeviceFileNotConnected: "设备未连接，无法导入文件",
    importDownloadDeviceFileConnected: "设备已连接，调用服务层导入文件",
    importDownloadDeviceFileSuccess: "文件导入成功，结果",
    importDownloadDeviceFileException: "文件导入异常",
    exportDownloadDeviceFileStart: "开始导出下载的设备文件，请求参数",
    exportDownloadDeviceFileNotConnected: "设备未连接，无法导出文件",
    exportDownloadDeviceFileEmptyPath: "导出路径为空",
    exportDownloadDeviceFileCallService: "调用服务层导出文件",
    exportDownloadDeviceFileSuccess: "文件导出成功，结果",
    exportDownloadDeviceFileException: "文件导出异常"
  },
  deviceOperationController: {
    initialized: "控制器初始化完成",
    manualWaveStart: "开始手动录波，请求参数",
    manualWaveCheckConnection: "检查设备连接状态",
    manualWaveNotConnected: "设备未连接，无法执行手动录波",
    manualWaveConnected: "设备已连接，调用服务层执行手动录波",
    manualWaveSuccess: "手动录波执行成功，结果",
    manualWaveException: "手动录波执行异常",
    clearWaveStart: "开始清除录波报告，请求参数",
    clearWaveCheckConnection: "检查设备连接状态",
    clearWaveNotConnected: "设备未连接，无法清除录波报告",
    clearWaveConnected: "设备已连接，调用服务层清除录波报告",
    clearWaveSuccess: "清除录波报告成功，结果",
    clearWaveException: "清除录波报告异常",
    resetDeviceStart: "开始装置复归，请求参数",
    resetDeviceCheckConnection: "检查设备连接状态",
    resetDeviceNotConnected: "设备未连接，无法执行装置复归",
    resetDeviceConnected: "设备已连接，调用服务层执行装置复归",
    resetDeviceSuccess: "装置复归执行成功，结果",
    resetDeviceException: "装置复归执行异常",
    clearReportStart: "开始清除报告，请求参数",
    clearReportCheckConnection: "检查设备连接状态",
    clearReportNotConnected: "设备未连接，无法清除报告",
    clearReportConnected: "设备已连接，调用服务层清除报告",
    clearReportSuccess: "清除报告成功，结果",
    clearReportException: "清除报告异常",
    rebootDeviceStart: "开始装置重启，请求参数",
    rebootDeviceCheckConnection: "检查设备连接状态",
    rebootDeviceNotConnected: "设备未连接，无法执行装置重启",
    rebootDeviceConnected: "设备已连接，调用服务层执行装置重启",
    rebootDeviceSuccess: "装置重启执行成功，结果",
    rebootDeviceException: "装置重启执行异常"
  },
  deviceSummaryController: {
    initialized: "控制器初始化完成",
    getSGCountStart: "开始获取SG数量，请求参数",
    getSGCountCheckConnection: "检查设备连接状态，设备ID",
    getSGCountNotConnected: "设备未连接，无法获取SG数量，设备ID",
    getSGCountConnected: "设备已连接，调用服务层获取SG数量",
    getSGCountSuccess: "成功获取SG数量，结果",
    getSGCountException: "获取SG数量异常，设备ID",
    getSummaryInfoStart: "开始获取装置汇总信息，请求参数",
    getSummaryInfoCheckConnection: "检查设备连接状态",
    getSummaryInfoNotConnected: "设备未连接，无法获取装置汇总信息",
    getSummaryInfoConnected: "设备已连接，调用服务层获取装置汇总信息",
    getSummaryInfoSuccess: "成功获取装置汇总信息，结果",
    getSummaryInfoException: "获取装置汇总信息异常",
    getParamSummaryStart: "开始获取参数定值汇总信息，请求参数",
    getParamSummaryCheckConnection: "检查设备连接状态",
    getParamSummaryNotConnected: "设备未连接，无法获取参数定值汇总信息",
    getParamSummaryConnected: "设备已连接，调用服务层获取参数定值汇总信息",
    getParamSummarySuccess: "成功获取参数定值汇总信息，结果",
    getParamSummaryException: "获取参数定值汇总信息异常"
  },
  deviceTimeController: {
    initialized: "控制器初始化完成",
    getDeviceTimeStart: "开始获取设备时间，请求参数",
    getDeviceTimeCheckConnection: "检查设备连接状态",
    getDeviceTimeNotConnected: "设备未连接，无法获取设备时间",
    getDeviceTimeConnected: "设备已连接，调用服务层获取设备时间",
    getDeviceTimeSuccess: "成功获取设备时间，结果",
    getDeviceTimeException: "获取设备时间异常",
    writeDeviceTimeStart: "开始设置设备时间，请求参数",
    writeDeviceTimeCheckConnection: "检查设备连接状态",
    writeDeviceTimeNotConnected: "设备未连接，无法设置设备时间",
    writeDeviceTimeConnected: "设备已连接，调用服务层设置设备时间",
    writeDeviceTimeSuccess: "成功设置设备时间，结果",
    writeDeviceTimeException: "设置设备时间异常"
  },
  realEventController: {
    initialized: "控制器初始化完成",
    subRealEventStart: "开始订阅实时事件，请求参数",
    subRealEventCallService: "调用服务层订阅实时事件",
    subRealEventException: "订阅实时事件异常",
    subRealEventServiceResult: "订阅实时事件服务层返回结果",
    subRealEventFailed: "订阅实时事件失败，错误信息",
    subRealEventServiceError: "订阅事件服务错误，错误码",
    subRealEventSuccess: "订阅实时事件成功",
    unSubRealEventStart: "开始取消订阅实时事件，请求参数",
    unSubRealEventCallService: "调用服务层取消订阅实时事件",
    unSubRealEventException: "取消订阅实时事件异常",
    unSubRealEventServiceResult: "取消订阅实时事件服务层返回结果",
    unSubRealEventFailed: "取消订阅实时事件失败，错误信息",
    unSubRealEventServiceError: "取消订阅事件服务错误，错误码",
    unSubRealEventSuccess: "取消订阅实时事件成功"
  },
  remoteYxAndYcController: {
    ykycGetGroupDataEntry: "ykycGetGroupData 入参",
    ykycGetGroupDataReturn: "ykycGetGroupData 返回",
    ykycGetGroupDataException: "ykycGetGroupData 异常",
    exportAllDataEntry: "exportAllData 入参",
    exportAllDataEmptyPath: "导出路径不能为空",
    exportAllDataReturn: "exportAllData 返回",
    exportAllDataException: "exportAllData 异常"
  },





  // 新增的服务日志
  customInfoService: {
    getAllGroupsEntry: "getAllGroups 入参",
    addMenuEntry: "addMenu 入参",
    groupName: "分组名称",
    editMenuEntry: "editMenu 入参",
    newGroupKeyword: "新分组关键字",
    deleteMenuEntry: "deleteMenu 入参",
    addReportEntry: "addReport 入参",
    reportName: "报告名称",
    newName: "新名称",
    editReportEntry: "editReport 入参",
    newReportName: "新报告名称",
    deleteReportEntry: "deleteReport 入参",
    getLGReportsEntry: "getLGReports 入参",
    getLGReportsSuccess: "getLGReports 成功",
    lgReportsCount: "LG报告数量",
    getLGReportsError: "getLGReports 异常"
  },

  deviceOperationService: {
    rebootDeviceEntry: "rebootDevice 入参",
    rebootDeviceStart: "重启装置开始",
    rebootDeviceReturn: "rebootDevice 返回",
    rebootDeviceError: "rebootDevice 异常",
    clearReportEntry: "clearReport 入参",
    clearReportStart: "清除报告开始",
    clearReportReturn: "clearReport 返回",
    clearReportError: "clearReport 异常",
    resetDeviceEntry: "resetDevice 入参",
    resetDeviceStart: "装置复归开始",
    resetDeviceReturn: "resetDevice 返回",
    resetDeviceError: "resetDevice 异常",
    clearWaveEntry: "clearWave 入参",
    clearWaveStart: "清除录波开始",
    clearWaveReturn: "clearWave 返回",
    clearWaveError: "clearWave 异常",
    manualWaveEntry: "manualWave 入参",
    manualWaveStart: "手动录波开始",
    manualWaveReturn: "manualWave 返回",
    manualWaveError: "manualWave 异常"
  },

  deviceSummaryService: {
    getSGCountEntry: "getSGCount 入参",
    getSGCountReturn: "getSGCount 返回",
    getSGCountError: "getSGCount 异常",
    getSummaryInfoEntry: "getSummaryInfo 入参",
    getSummaryInfoReturn: "getSummaryInfo 返回",
    getSummaryInfoError: "getSummaryInfo 异常",
    getParamSummaryEntry: "getParamSummary 入参",
    getParamSummaryReturn: "getParamSummary 返回",
    getParamSummaryError: "getParamSummary 异常"
  },

  deviceTimeService: {
    getDeviceTimeEntry: "getDeviceTime 入参",
    getDeviceTimeReturn: "getDeviceTime 返回",
    getDeviceTimeError: "getDeviceTime 异常",
    writeDeviceTimeEntry: "writeDeviceTime 入参",
    writeDeviceTimeReturn: "writeDeviceTime 返回",
    writeDeviceTimeError: "writeDeviceTime 异常"
  },

  remoteYxAndYcService: {
    ykycGetGroupDataEntry: "ykycGetGroupData 入参",
    ykycGetGroupDataReturn: "ykycGetGroupData 返回",
    ykycGetGroupDataError: "ykycGetGroupData 异常",
    ykycGetAllGroupDataEntry: "ykycGetAllGroupData 入参",
    exportAllValError: "exportAllVal 异常"
  },


};
