export default {
  search: {
    placeholder: "Buscar por palabra clave"
  },
  categories: {
    title: "📦Herramientas IT",
    formatting: "📝Herramientas de Formato",
    xml: "🟡Formato XML",
    json: "🟡Formato JSON",
    conversion: "🔄Herramientas de Conversión",
    radix: "🟢Conversión de Base",
    temperature: "🟢Conversión de Temperatura",
    encryption: "🔑Herramientas de Encriptación",
    textEncryption: "🔵Encriptación de Texto"
  },
  encryption: {
    title: "Encriptación de Texto",
    description: "Encripta y desencripta texto plano usando algoritmos como AES, TripleDES, Rabbit o RC4",
    encrypt: "Encriptar",
    inputText: "Texto a encriptar:",
    inputPlaceholder: "Ingrese el texto que necesita encriptar...",
    key: "Clave:",
    keyPlaceholder: "Ingrese la clave de encriptación",
    algorithm: "Algoritmo de encriptación:",
    outputText: "Texto encriptado:",
    outputPlaceholder: "El resultado de la encriptación se mostrará aquí...",
    decrypt: "Desencriptar",
    decryptInputText: "Texto a desencriptar:",
    decryptInputPlaceholder: "Ingrese el texto cifrado que necesita desencriptar...",
    decryptKey: "Clave:",
    decryptAlgorithm: "Algoritmo de desencriptación:",
    decryptOutputText: "Texto desencriptado:",
    decryptError: "No se puede desencriptar el texto"
  },
  json: {
    title: "Formato JSON",
    description: "Formatea cadenas JSON para una visualización legible",
    sortKeys: "Ordenar",
    indentSize: "Sangría",
    inputLabel: "JSON a formatear",
    inputPlaceholder: "Pega tu JSON...",
    outputLabel: "JSON formateado",
    invalid: "El documento no cumple con el estándar JSON, por favor verifique"
  },
  xml: {
    title: "Formato XML",
    description: "Formatea cadenas XML para una visualización legible",
    collapseContent: "Colapsar contenido:",
    indentSize: "Tamaño de sangría:",
    inputLabel: "Ingresar XML",
    inputPlaceholder: "Pega tu XML...",
    outputLabel: "XML formateado",
    invalid: "El documento no cumple con el estándar XML, por favor verifique"
  },
  temperature: {
    title: "Conversión de Temperatura",
    description: "Conversión entre Kelvin, Celsius, Fahrenheit, Rankine, Delisle, Newton, Réaumur y Rømer",
    kelvin: "Kelvin",
    kelvinUnit: "K",
    celsius: "Celsius",
    celsiusUnit: "°C",
    fahrenheit: "Fahrenheit",
    fahrenheitUnit: "°F",
    rankine: "Rankine",
    rankineUnit: "°R",
    delisle: "Delisle",
    delisleUnit: "°De",
    newton: "Newton",
    newtonUnit: "°N",
    reaumur: "Réaumur",
    reaumurUnit: "°Ré",
    romer: "Rømer",
    romerUnit: "°Rø"
  },
  radix: {
    title: "Conversión de Base",
    description: "Convierte números entre diferentes bases (decimal, hexadecimal, binario, octal, base64, etc.)",
    inputLabel: "Número a convertir",
    inputPlaceholder: "Ingrese un número (ej: 100)",
    outputLabel: "Resultado de conversión",
    binary: "Binario (2)",
    binaryPlaceholder: "Resultado binario...",
    octal: "Octal (8)",
    octalPlaceholder: "Resultado octal...",
    decimal: "Decimal (10)",
    decimalPlaceholder: "Resultado decimal...",
    hex: "Hexadecimal (16)",
    hexPlaceholder: "Resultado hexadecimal...",
    base64: "Base64 (64)",
    base64Placeholder: "Resultado Base64...",
    customBase: "Base personalizada",
    customBasePlaceholder: "Resultado Base {{base}}..."
  },
  jsonViewer: {
    title: "Formato JSON",
    description: "Formatea cadenas JSON para una visualización legible",
    sortKeys: "Ordenar",
    indentSize: "Sangría",
    inputJson: "JSON a formatear",
    formattedJson: "JSON formateado",
    placeholder: "Pega tu JSON...",
    validationError: "El documento no cumple con el estándar JSON. Por favor verifique"
  }
};
